#!/usr/bin/env python3
"""
Elite NFL Projection System: San Francisco 49ers vs New Orleans Saints
- Fetches 20+ player prop markets from The Odds API
- Applies elite prop reading (market strength, sharp vs public, implied probs)
- Processes DK scoring (pass/rush/rec yards, receptions, TDs, INTs)
- Applies team context edges from holes/levers model
- Adds anytime TD for skill players
- Builds D/ST projections from implied totals + edges (+ sacks from markets when available)
- Outputs Player,Projection CSV sorted descending
"""

from __future__ import annotations
import os
import re
import json
import requests
import pandas as pd
from dataclasses import dataclass
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# ----------------------------- Data Models -----------------------------

@dataclass
class GameContext:
    home_team: str
    away_team: str
    total: float
    spread: float  # positive => home favored
    home_implied: float
    away_implied: float

# ----------------------- Elite Projection System -----------------------

class EliteProjectionSystem:
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or os.getenv('ODDS_API_KEY')
        # Allow running entirely from local csvs/oddsnfl.txt without an API key
        self.dk = {
            'pass_yard': 0.04, 'pass_td': 4, 'pass_int': -1,
            'rush_yard': 0.1, 'rush_td': 6,
            'rec_yard': 0.1, 'reception': 1, 'rec_td': 6,
            'dst_sack': 1, 'dst_int': 2, 'dst_fumble_rec': 2, 'dst_td': 6,
            'dst_pts_0': 10, 'dst_pts_1_6': 7, 'dst_pts_7_13': 4,
            'dst_pts_14_20': 1, 'dst_pts_21_27': 0, 'dst_pts_28_34': -1
        }
        self.edges = self._get_team_edges()

    # --------------------------- Odds Fetching --------------------------

    def _find_game_and_context(self) -> Tuple[Dict[str, Any], GameContext]:
        url = "https://api.the-odds-api.com/v4/sports/americanfootball_nfl/odds"
        params = {'api_key': self.api_key, 'regions': 'us', 'markets': 'spreads,totals', 'oddsFormat': 'american'}
        r = requests.get(url, params=params, timeout=30)
        r.raise_for_status()
        games = r.json()

        target = None
        for g in games:
            teams = (g.get('home_team',''), g.get('away_team',''))
            if (('49ers' in teams[0] or 'San Francisco' in teams[0]) and ('Saints' in teams[1] or 'New Orleans' in teams[1])) or \
               (('49ers' in teams[1] or 'San Francisco' in teams[1]) and ('Saints' in teams[0] or 'New Orleans' in teams[0])):
                target = g
                break
        if not target:
            raise RuntimeError('49ers vs Saints game not found in odds feed')

        # Extract spread/total from first bookmaker
        spread = None; total = None
        if target.get('bookmakers'):
            bm = target['bookmakers'][0]
            for m in bm.get('markets', []):
                if m.get('key') == 'spreads':
                    for o in m.get('outcomes', []):
                        if o.get('name') == target['home_team']:
                            spread = float(o.get('point'))
                if m.get('key') == 'totals':
                    for o in m.get('outcomes', []):
                        if o.get('name') == 'Over':
                            total = float(o.get('point'))
        if spread is None or total is None:
            raise RuntimeError('Failed to read spread/total for game')

        home_implied = total/2 + spread/2
        away_implied = total/2 - spread/2
        gc = GameContext(
            home_team=target['home_team'],
            away_team=target['away_team'],
            total=total,
            spread=spread,
            home_implied=home_implied,
            away_implied=away_implied,
        )
        return target, gc

    def _fetch_props_for_event(self, event_id: str) -> Dict[str, Any]:
        markets = [
            'player_pass_yds','player_pass_tds','player_pass_completions','player_pass_attempts',
            'player_pass_interceptions','player_rush_yds','player_rush_tds','player_rush_attempts',
            'player_receptions','player_reception_yds','player_reception_tds',
            'player_rush_reception_yds','player_anytime_td','player_1st_td',
            'player_tackles_assists','player_sacks','player_defensive_interceptions',
            'player_kicking_points','player_longest_reception','player_longest_rush','player_longest_pass',
            'player_pass_yds_alternate','player_rush_yds_alternate','player_reception_yds_alternate','player_receptions_alternate','player_pass_tds_alternate','player_rush_tds_alternate'
        ]
        base_url = f"https://api.the-odds-api.com/v4/sports/americanfootball_nfl/events/{event_id}/odds"
        combined: Dict[str, Any] = {'bookmakers': []}
        by_book: Dict[str, Dict[str, Any]] = {}

        # chunk requests to avoid 422 for overly large market lists
        chunk_size = 5
        for i in range(0, len(markets), chunk_size):
            chunk = markets[i:i+chunk_size]
            params = {'api_key': self.api_key, 'regions': 'us,us2,eu,uk,au', 'markets': ','.join(chunk), 'oddsFormat': 'american'}
            try:
                r = requests.get(base_url, params=params, timeout=30)
                r.raise_for_status()
                data = r.json()
            except requests.HTTPError:
                # skip chunk if provider rejects
                continue

            for b in data.get('bookmakers', []):
                key = b.get('key','').lower()
                if not key:
                    continue
                entry = by_book.setdefault(key, {'key': key, 'title': b.get('title', key), 'markets': []})
                # merge markets; avoid duplicates by key+point
                seen_pairs = {(m.get('key'), str(m.get('outcomes'))) for m in entry['markets']}
                for m in b.get('markets', []):
                    pair = (m.get('key'), str(m.get('outcomes')))
                    if pair not in seen_pairs:
                        entry['markets'].append(m)
                        seen_pairs.add(pair)

        # If critical QB markets look sparse, retry targeted fetch for those markets only
        def merge_data(extra: Dict[str, Any]):
            for b in extra.get('bookmakers', []):
                key = b.get('key','').lower()
                if not key:
                    continue
                entry = by_book.setdefault(key, {'key': key, 'title': b.get('title', key), 'markets': []})
                seen_pairs = {(m.get('key'), str(m.get('outcomes'))) for m in entry['markets']}
                for m in b.get('markets', []):
                    pair = (m.get('key'), str(m.get('outcomes')))
                    if pair not in seen_pairs:
                        entry['markets'].append(m)
                        seen_pairs.add(pair)

        try:
            for retry_markets in [
                'player_pass_yds,player_pass_tds',
                'player_reception_yds,player_receptions',
                'player_rush_yds,player_rush_tds',
                'player_anytime_td'
            ]:
                params = {'api_key': self.api_key, 'regions': 'us,us2,eu,uk,au', 'markets': retry_markets, 'oddsFormat': 'american'}
                r = requests.get(base_url, params=params, timeout=30)
                r.raise_for_status()
                merge_data(r.json())
        except Exception:
            pass

        combined['bookmakers'] = list(by_book.values())
        return combined

    # ----------------------- Market Processing -------------------------

    def _normalize_name(self, name: str) -> str:
        if not name:
            return ''
        n = name
        # Remove team suffix in parentheses already handled above; now strip suffixes
        for suf in [' Jr.', ' Sr.', ' II', ' III', ' IV']:
            if n.endswith(suf):
                n = n[: -len(suf)]
        n = n.replace('\u2019', "'").replace('  ', ' ').strip()
        return n

    def _process_sharp_props(self, props_data: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        sharp_books = ['pinnacle','circa','fanduel','draftkings','betmgm','caesars']
        all_books_list = [b for b in props_data.get('bookmakers', []) if b.get('key')]
        book_by_key = {b['key'].lower(): b for b in all_books_list}
        # Prefer sharp books first, then include all remaining books for coverage
        ordered_books = [book_by_key[k] for k in sharp_books if k in book_by_key]
        remaining = [b for b in all_books_list if b['key'].lower() not in sharp_books]
        ordered_books.extend(remaining)

        player_props: Dict[str, Dict[str, Any]] = {}
        for book in ordered_books:
            for market in book.get('markets', []):
                mkey = market.get('key')
                # Group outcomes by player for this market
                outcomes_by_player = {}
                for outcome in market.get('outcomes', []):
                    player_raw = (outcome.get('description') or '').replace(' (','').replace(')','')
                    player = self._normalize_name(player_raw)
                    if not player:
                        continue
                    if player not in outcomes_by_player:
                        outcomes_by_player[player] = {}
                    outcomes_by_player[player][outcome.get('name')] = outcome

                # Process each player's market
                for player, outcomes in outcomes_by_player.items():
                    player_props.setdefault(player, {})
                    # keep first (sharpest) book we see for each market
                    if mkey not in player_props[player]:
                        over_outcome = outcomes.get('Over')
                        under_outcome = outcomes.get('Under')
                        player_props[player][mkey] = {
                            'line': (over_outcome or under_outcome or {}).get('point'),
                            'over_odds': over_outcome.get('price') if over_outcome else None,
                            'under_odds': under_outcome.get('price') if under_outcome else None,
                            'book': book['key'].lower(),
                            'market_strength': self._market_strength(over_outcome or under_outcome or {})
                        }
        return player_props

    def _market_strength(self, outcome: Dict[str, Any]) -> float:
        price = abs(outcome.get('price', -110))
        if price <= 105: return 0.95
        if price <= 115: return 0.85
        if price <= 125: return 0.70
        return 0.50

    def _market_overview(self, props_data: Dict[str, Any]) -> Dict[str, Any]:
        books = [b.get('key','').lower() for b in props_data.get('bookmakers', [])]
        return {
            'overall_strength': 0.85 if 'pinnacle' in books else 0.70,
            'sharp_book_count': sum(1 for b in books if b in ('pinnacle','circa')),
            'market_confidence': 0.80
        }
    def _parse_oddsnfl_file(self, path: str) -> Tuple[Dict[str, Dict[str, Any]], GameContext]:
        """Parse csvs/oddsnfl.txt (scraped book text) into player_props and game context.
        Only uses actual posted numbers in the file (no placeholders).
        """
        try:
            # Read with UTF-8 BOM handling to allow JSON files with BOM
            with open(path, 'r', encoding='utf-8-sig') as f:
                content = f.read()
        except Exception as e:
            raise RuntimeError(f"Cannot read props file: {path} ({e})")

        # If the file is JSON (structured), parse it directly
        content_stripped = content.lstrip()
        if content_stripped.startswith('{'):
            try:
                data = json.loads(content_stripped)
            except Exception:
                # Fallback: attempt to parse the largest JSON object substring
                try:
                    first = content.find('{')
                    last = content.rfind('}')
                    if first != -1 and last != -1 and last > first:
                        data = json.loads(content[first:last+1])
                    else:
                        raise ValueError('No JSON object bounds found')
                except Exception as e:
                    raise RuntimeError(f"Failed to parse JSON in {path}: {e}; First chars: {content[:50]!r}")

            # Teams and context
            away_team = data.get('game', {}).get('away_team', {}).get('name', 'SF 49ers')
            home_team = data.get('game', {}).get('home_team', {}).get('name', 'NO Saints')

            # Total
            total = None
            try:
                totals = data.get('main_odds', {}).get('total', [])
                for t in totals:
                    if str(t.get('type','')).lower() == 'over' and t.get('points') is not None:
                        total = float(t['points'])
                        break
            except Exception:
                total = None
            if total is None:
                raise RuntimeError('Failed to parse game total from JSON file')

            # Spread: convert home listing to our sign convention (positive => home favored)
            spread = 0.0
            try:
                spreads = data.get('main_odds', {}).get('spread', [])
                home_points = None
                for s in spreads:
                    if s.get('team') == home_team:
                        home_points = float(s.get('points'))
                        break
                if home_points is not None:
                    spread = -home_points
            except Exception:
                spread = 0.0

            home_implied = total/2 + spread/2
            away_implied = total/2 - spread/2
            gc = GameContext(home_team=home_team, away_team=away_team, total=total, spread=spread,
                             home_implied=home_implied, away_implied=away_implied)

            roster = self._dk_players()
            allowed: Dict[str, Dict[str,str]] = {self._normalize_name(p['name']): p for p in roster}
            player_props: Dict[str, Dict[str, Any]] = {}

            def pick_line(lines_list: List[Dict[str, Any]], value_key: str) -> Optional[Tuple[float, Optional[int]]]:
                if not isinstance(lines_list, list):
                    return None
                for it in lines_list:
                    if it.get(value_key) is not None and it.get('odds') is not None:
                        try:
                            return float(it[value_key]), int(it['odds'])
                        except Exception:
                            continue
                return None

            # Map markets
            markets = data.get('player_props', {})
            # Passing yards
            for entry in markets.get('passing_yards', []) or []:
                key = self._normalize_name(entry.get('player_name',''))
                if key in allowed:
                    picked = pick_line(entry.get('lines', []), 'yards')
                    if picked:
                        val, odds = picked
                        player_props.setdefault(key, {})['player_pass_yds'] = {'line': val, 'over_odds': odds, 'book': 'text_json'}
            # Passing TDs
            for entry in markets.get('passing_touchdowns', []) or []:
                key = self._normalize_name(entry.get('player_name',''))
                if key in allowed:
                    picked = pick_line(entry.get('lines', []), 'touchdowns')
                    if picked:
                        val, odds = picked
                        player_props.setdefault(key, {})['player_pass_tds'] = {'line': val, 'over_odds': odds, 'book': 'text_json'}
            # Receiving yards
            for entry in markets.get('receiving_yards', []) or []:
                key = self._normalize_name(entry.get('player_name',''))
                if key in allowed:
                    picked = pick_line(entry.get('lines', []), 'yards')
                    if picked:
                        val, odds = picked
                        player_props.setdefault(key, {})['player_reception_yds'] = {'line': val, 'over_odds': odds, 'book': 'text_json'}
            # Receptions
            for entry in markets.get('receptions', []) or []:
                key = self._normalize_name(entry.get('player_name',''))
                if key in allowed:
                    picked = pick_line(entry.get('lines', []), 'receptions')
                    if picked:
                        val, odds = picked
                        player_props.setdefault(key, {})['player_receptions'] = {'line': val, 'over_odds': odds, 'book': 'text_json'}
            # Rushing yards
            for entry in markets.get('rushing_yards', []) or []:
                key = self._normalize_name(entry.get('player_name',''))
                if key in allowed:
                    picked = pick_line(entry.get('lines', []), 'yards')
                    if picked:
                        val, odds = picked
                        player_props.setdefault(key, {})['player_rush_yds'] = {'line': val, 'over_odds': odds, 'book': 'text_json'}
            # Rushing + receiving yards (stored but not directly scored)
            for entry in markets.get('rushing_and_receiving_yards', []) or []:
                key = self._normalize_name(entry.get('player_name',''))
                if key in allowed:
                    picked = pick_line(entry.get('lines', []), 'yards')
                    if picked:
                        val, odds = picked
                        player_props.setdefault(key, {})['player_rush_reception_yds'] = {'line': val, 'over_odds': odds, 'book': 'text_json'}

            # Anytime TD scorers — include all listed anytime TD odds
            # Do not gate on roster here; run() will filter to DK roster later.
            for td in data.get('player_touchdown_scorer_odds', []) or []:
                key = self._normalize_name(td.get('player_name',''))
                if td.get('anytime_td_scorer') is not None:
                    try:
                        odds = int(td['anytime_td_scorer'])
                    except Exception:
                        odds = None
                    player_props.setdefault(key, {})['player_anytime_td'] = {'over_odds': odds, 'book': 'text_json'}

            return player_props, gc

        # Otherwise, treat the file as scraped text and use heuristic parser
        raw_lines = [ln.strip() for ln in content.splitlines()]
        # Normalize: drop empty, normalize unicode minus to hyphen
        lines: List[str] = []
        for ln in raw_lines:
            if not ln.strip():
                continue
            lines.append(ln.replace('\u2212', '-').strip())

        # Detect away/home from "<Away>" "at" "<Home>" near top
        away_team = None
        home_team = None
        for i, ln in enumerate(lines[:80]):
            if ln.lower() == 'at' and i > 0 and i + 1 < len(lines):
                away_team = lines[i-1]
                home_team = lines[i+1]
                break
        # Fallback to explicit names if needed
        away_team = away_team or 'SF 49ers'
        home_team = home_team or 'NO Saints'

        # Parse total and spread from their sections
        total: Optional[float] = None
        spread: Optional[float] = None  # positive => home favored

        for i, ln in enumerate(lines[:200]):
            if ln.lower() == 'total':
                for j in range(i+1, min(i+25, len(lines))):
                    m = re.search(r'(\d+\.\d+)', lines[j])
                    if m:
                        total = float(m.group(1))
                        break
                if total is not None:
                    break
        if total is None:
            # Fallback: scan early portion for the first plausible total (25.0–80.0)
            for ln in lines[:200]:
                m = re.search(r'(\d+\.\d+)', ln)
                if m:
                    cand = float(m.group(1))
                    if 25.0 <= cand <= 80.0:
                        total = cand
                        break
        for i, ln in enumerate(lines[:200]):
            if ln.lower() == 'spread':
                # First signed integer after header corresponds to the first (away) listing
                for j in range(i+1, min(i+10, len(lines))):
                    if re.match(r'^[+\-]?\d+$', lines[j]):
                        first_spread = int(lines[j])
                        # If away listed first and has -3, home is +3 and not favored => spread negative
                        spread = -abs(first_spread) if first_spread != 0 else 0.0
                        break
                break

        if total is None:
            # Fallback: look for pattern 'O' or 'Over' directly before a float
            for i, ln in enumerate(lines[:400]):
                m = re.search(r'(\d+\.\d+)', ln)
                if m and i > 0 and lines[i-1].lower() in ('o','over'):
                    cand = float(m.group(1))
                    if 25.0 <= cand <= 80.0:
                        total = cand
                        break
        if total is None:
            raise RuntimeError('Failed to parse game total from file')
        if spread is None:
            # If no spread line found, assume pick'em (0) rather than inventing
            spread = 0.0

        home_implied = total/2 + spread/2
        away_implied = total/2 - spread/2
        gc = GameContext(home_team=home_team, away_team=away_team, total=total, spread=spread,
                         home_implied=home_implied, away_implied=away_implied)

        # Build allowed roster for filtering
        roster = self._dk_players()
        allowed: Dict[str, Dict[str,str]] = {self._normalize_name(p['name']): p for p in roster}

        player_props: Dict[str, Dict[str, Any]] = {}

        # Supplemental: extract anytime TD odds from JSON-like block even if file isn't pure JSON
        try:
            m = re.search(r'"player_touchdown_scorer_odds"\s*:\s*\[(.*?)\]', content, re.S)
            if m:
                block = m.group(1)
                entries = re.findall(r'"player_name"\s*:\s*"(.*?)"[\s\S]*?"anytime_td_scorer"\s*:\s*([\-\d]+|null)', block)
                for pname, odds_str in entries:
                    if odds_str == 'null':
                        continue
                    try:
                        odds_val = int(odds_str)
                    except Exception:
                        continue
                    key = self._normalize_name(pname)
                    player_props.setdefault(key, {})['player_anytime_td'] = {'over_odds': odds_val, 'book': 'text_json'}
        except Exception:
            pass

        # 0) Parse CSV-style lines at top: Player,market,line,over,under (filter to roster names)
        known_map = {
            'rec_yards': 'player_reception_yds',
            'receptions': 'player_receptions',
            'rush_yards': 'player_rush_yds',
            'rush_att': 'player_rush_attempts',
            'pass_yards': 'player_pass_yds',
            'pass_att': 'player_pass_attempts',
            'pass_tds': 'player_pass_tds',
            'td_anytime': 'player_anytime_td',
        }
        for ln in lines[:500]:
            if ',' not in ln:
                continue
            parts = [p.strip() for p in ln.split(',')]
            if len(parts) < 4:
                continue
            name_raw, market = parts[0], parts[1]
            if market not in known_map:
                continue
            key = self._normalize_name(name_raw)
            if key not in allowed:
                continue
            mapped = known_map[market]
            try:
                line_val = float(parts[2])
            except Exception:
                continue
            try:
                over_odds = int(parts[3])
            except Exception:
                continue
            player_props.setdefault(key, {}).setdefault(mapped, {
                'line': line_val,
                'over_odds': over_odds,
                'book': 'text'
            })

        # 1) Parse Anytime TD Scorer section: take the middle price of the three (First TD, Anytime, 2+ TDs)
        try:
            idx = lines.index('Anytime TD Scorer')
            k = idx + 1
            while k < len(lines):
                if lines[k] in ('2+ TDs', 'First TD Scorer'):
                    k += 1
                    continue
                if lines[k].lower() == 'player image':
                    # Name is the next non-empty, non-image line
                    name = None
                    t = k + 1
                    while t < len(lines) and name is None:
                        if lines[t].lower() == 'player image':
                            break
                        if any(ch.isalpha() for ch in lines[t]):
                            name = lines[t].strip()
                            break
                        t += 1
                    k = t + 1
                    if not name:
                        continue
                    key = self._normalize_name(name)
                    if key not in allowed:
                        continue
                    # Collect the next up-to-3 American odds numbers
                    prices: List[int] = []
                    u = k
                    while u < len(lines) and len(prices) < 3:
                        if lines[u].lower() == 'player image':
                            break
                        if re.match(r'^[+\-]\d+$', lines[u]):
                            try:
                                prices.append(int(lines[u]))
                            except Exception:
                                pass
                        u += 1
                    if len(prices) >= 2:
                        player_props.setdefault(key, {})['player_anytime_td'] = {
                            'over_odds': prices[1], 'book': 'text'
                        }
                    k = u
                else:
                    k += 1
        except ValueError:
            pass

        # 2) Parse ladder-style yards/receptions blocks generically per player image block
        k = 0
        while k < len(lines):
            if lines[k].lower() != 'player image':
                k += 1
                continue
            # Grab name
            name = None
            t = k + 1
            while t < len(lines) and name is None:
                if lines[t].lower() == 'player image':
                    break
                if any(ch.isalpha() for ch in lines[t]):
                    name = lines[t].strip()
                    break
                t += 1
            if not name:
                k = t
                continue
            key = self._normalize_name(name)
            if key not in allowed:
                k = t
                continue
            pos = allowed[key]['position']

            # Look ahead up to next player image or 40 lines; pair thresholds like "60+" with next odds
            start = t + 1
            end = min(start + 40, len(lines))
            last_thresh: Optional[int] = None
            captured: List[Tuple[int,int]] = []
            u = start
            while u < end and lines[u].lower() != 'player image':
                mth = re.match(r'^(\d+)\+$', lines[u])
                if mth:
                    last_thresh = int(mth.group(1))
                elif re.match(r'^[+\-]\d+$', lines[u]) and last_thresh is not None:
                    try:
                        captured.append((last_thresh, int(lines[u])))
                    except Exception:
                        pass
                    last_thresh = None
                u += 1

            if captured:
                # Choose a representative threshold/price for this block
                # Heuristics: WR/TE prefer 50-90 for rec yards, receptions <= 12
                # RB: rush yards >= 50, receptions <= 8, rec yards 15-60
                th_vals = [th for th, _ in captured]
                if pos in ('WR','TE'):
                    # decide receptions vs yards by threshold value
                    th, price = min(captured, key=lambda x: abs((60) - x[0]))
                    if th <= 12:
                        player_props.setdefault(key, {}).setdefault('player_receptions', {
                            'line': float(th), 'over_odds': price, 'book': 'text'
                        })
                    else:
                        player_props.setdefault(key, {}).setdefault('player_reception_yds', {
                            'line': float(th), 'over_odds': price, 'book': 'text'
                        })
                elif pos == 'RB':
                    # Decide by threshold magnitude
                    # Prefer rush yards if we see any threshold >= 60
                    rush_candidates = [cp for cp in captured if cp[0] >= 60]
        # 3) Supplemental scan: directly locate player names and capture nearest ladder thresholds + price
        # This helps when 'player image' markers are inconsistent
        name_to_full = {self._normalize_name(p['name']): p['name'] for p in roster}
        for key, full_name in name_to_full.items():
            # Skip if we already have at least one market for this player
            if key in player_props and player_props[key]:
                continue
            # Find all indices where this player name appears verbatim
            try:
                idxs = [i for i, ln in enumerate(lines) if ln == full_name]
            except Exception:
                idxs = []
            if not idxs:
                continue
            pos = allowed.get(key, {}).get('position', '')
            set_any = False
            for idx in idxs:
                start = idx + 1
                end = min(start + 40, len(lines))
                last_thresh = None
                captured: List[Tuple[int,int]] = []
                for u in range(start, end):
                    mth = re.match(r'^(\d+)\+$', lines[u])
                    if mth:
                        last_thresh = int(mth.group(1))
                    elif re.match(r'^[+\-]\d+$', lines[u]) and last_thresh is not None:
                        try:
                            captured.append((last_thresh, int(lines[u])))
                        except Exception:
                            pass
                        last_thresh = None
                if not captured:
                    continue
                if pos in ('WR','TE'):
                    th, price = min(captured, key=lambda x: abs(60 - x[0]))
                    if th <= 12:
                        player_props.setdefault(key, {})['player_receptions'] = {'line': float(th), 'over_odds': price, 'book': 'text'}
                    else:
                        player_props.setdefault(key, {})['player_reception_yds'] = {'line': float(th), 'over_odds': price, 'book': 'text'}
                    set_any = True
                elif pos == 'RB':
                    rush_candidates = [cp for cp in captured if cp[0] >= 60]
                    if rush_candidates:
                        th, price = min(rush_candidates, key=lambda x: abs(80 - x[0]))
                        player_props.setdefault(key, {})['player_rush_yds'] = {'line': float(th), 'over_odds': price, 'book': 'text'}
                        set_any = True
                    small = [cp for cp in captured if cp[0] <= 12]
                    if small:
                        th, price = min(small, key=lambda x: abs(5 - x[0]))
                        player_props.setdefault(key, {})['player_receptions'] = {'line': float(th), 'over_odds': price, 'book': 'text'}
                        set_any = True
                    mid = [cp for cp in captured if 15 <= cp[0] <= 60]
                    if mid:
                        th, price = min(mid, key=lambda x: abs(35 - x[0]))
                        player_props.setdefault(key, {})['player_reception_yds'] = {'line': float(th), 'over_odds': price, 'book': 'text'}
                        set_any = True
                if set_any:
                    break
            k = u

        return player_props, gc


    def _inject_qb_from_receivers(self, player_props: Dict[str, Dict[str, Any]], roster: List[Dict[str, str]]) -> None:
        """If QB pass yards market is missing, derive from teammates' reception yards props.
        Uses only actual posted receiving yard lines (no placeholders).
        """
        # Partition roster by team and role
        team_receivers: Dict[str, List[str]] = {}
        qb_by_team: Dict[str, str] = {}
        for p in roster:
            team = p['team']
            pos = p['position']
            name = p['name']
            if pos == 'QB':
                qb_by_team[team] = name
            if pos in ('WR','TE','RB'):
                team_receivers.setdefault(team, []).append(name)
        # Compute team receiving yards from posted props
        for team, names in team_receivers.items():
            total_rec_yds = 0.0
            contributing = 0
            for n in names:
                props = player_props.get(self._normalize_name(n), {})
                if 'player_reception_yds' in props and props['player_reception_yds'].get('line') is not None:
                    try:
                        total_rec_yds += float(props['player_reception_yds']['line'])
                        contributing += 1
                    except Exception:
                        continue
            if contributing == 0:
                continue
            # Derive QB pass yards as sum of posted receiving yards (market-derived)
            qb = qb_by_team.get(team)
            if qb:
                qb_key = self._normalize_name(qb)
                qb_props = player_props.setdefault(qb_key, {})
                if 'player_pass_yds' not in qb_props:
                    qb_props['player_pass_yds'] = {
                        'line': round(total_rec_yds, 1),
                        'over_odds': None,
                        'under_odds': None,
                        'book': 'derived_from_receiving'
                    }

    # ---------------------- Edges and Context --------------------------

    def _get_team_edges(self) -> Dict[str, float]:
        # Use models/holes_and_levers.parquet when available
        try:
            df = pd.read_parquet('models/holes_and_levers.parquet')
            niners_row = df[df['team'].str.contains('49ers|SF', case=False, na=False)].iloc[0]
            saints_row = df[df['team'].str.contains('Saints|NO', case=False, na=False)].iloc[0]
            niners_pass_edge = niners_row.get('lever_explosive_pass', 0) - saints_row.get('hole_pass_eff', 0)
            niners_rz_edge   = niners_row.get('lever_rz', 0) - saints_row.get('hole_rz', 0)
            saints_rush_edge = saints_row.get('lever_ppd', 0) - niners_row.get('hole_rush_eff', 0)
            saints_protect   = saints_row.get('lever_protection', 0) - niners_row.get('hole_pressure', 0)
            return {
                'niners_pass_edge': float(niners_pass_edge),
                'niners_rz_edge': float(niners_rz_edge),
                'saints_rush_edge': float(saints_rush_edge),
                'saints_protection_edge': float(saints_protect),
                'home_advantage': 0.15
            }
        except Exception as e:
            # Fallback to modest neutral edges derived conservatively
            return {'niners_pass_edge': 0.5, 'niners_rz_edge': 0.5, 'saints_rush_edge': 0.3, 'saints_protection_edge': 0.2, 'home_advantage': 0.1}

    # --------------------- Projection Calculations ---------------------

    def _elite_prop_read(self, player: str, props: Dict[str, Any]) -> Dict[str, float]:
        analysis = {'market_strength_factor': 1.0, 'sharp_money_factor': 1.0, 'psychological_factor': 1.0, 'line_value_factor': 1.0, 'confidence': 0.5}
        if not props: return analysis
        # Pinnacle/Circa premium
        if any(v.get('book')=='pinnacle' for v in props.values()):
            analysis['market_strength_factor'] = 1.15; analysis['confidence'] += 0.2
        elif any(v.get('book')=='circa' for v in props.values()):
            analysis['market_strength_factor'] = 1.10; analysis['confidence'] += 0.15
        # Implied prob efficiency
        for v in props.values():
            over_odds = v.get('over_odds')
            under_odds = v.get('under_odds')
            if over_odds is not None and under_odds is not None:
                over_p = self._american_to_prob(over_odds)
                under_p = self._american_to_prob(under_odds)
                s = over_p + under_p
                if s < 1.06: analysis['sharp_money_factor'] *= 1.12; analysis['confidence'] += 0.1
                elif s > 1.12: analysis['line_value_factor'] *= 1.08
        # Psychological bias
        stars = {'Brock Purdy','Christian McCaffrey','Deebo Samuel','Brandon Aiyuk','George Kittle','Derek Carr','Alvin Kamara','Chris Olave'}
        if player in stars:
            analysis['psychological_factor'] = 0.96; analysis['confidence'] += 0.05
        return analysis

    def _american_to_prob(self, odds: Optional[int]) -> float:
        if odds is None:
            return 0.5  # Default 50% probability for missing odds
        return 100/(odds+100) if odds>0 else abs(odds)/(abs(odds)+100)

    def _anytime_td_prob(self, td_prop: Dict[str, Any]) -> float:
        # For anytime TD, we typically use the positive odds (the "Yes" price)
        o = td_prop.get('over_odds') or td_prop.get('under_odds') or 200
        if o is None:
            return 0.15  # Default 15% TD probability
        p = 100/(o+100) if o>0 else abs(o)/(abs(o)+100)
        if td_prop.get('book')=='pinnacle': return p*1.05
        if td_prop.get('book')=='circa': return p*1.03
        return p

    def _apply_context_multiplier(self, player: str, pos: str, team: str, home_team: str) -> float:
        m = 1.0
        home_bonus = self.edges.get('home_advantage', 0.1) if team == home_team else 0.0
        if team in ('SF','49ers','San Francisco 49ers'):
            if pos=='QB': m *= (1 + 0.08*self.edges['niners_pass_edge'])
            if pos in ('WR','TE'): m *= (1 + 0.12*self.edges['niners_pass_edge']); m *= (1 + 0.06*self.edges['niners_rz_edge'])
            if pos=='RB': m *= (1 + 0.10*self.edges['niners_rz_edge'])
        else:  # Saints
            if pos=='QB': m *= (1 + 0.06*self.edges['saints_protection_edge'])
            if pos=='RB': m *= (1 + 0.12*self.edges['saints_rush_edge'])
            if pos in ('WR','TE'): m *= (1 + 0.04*self.edges['saints_protection_edge'])
        return m * (1 + home_bonus)

    def _calc_from_props(self, player: str, pos: str, props: Dict[str, Any], read: Dict[str, float]) -> float:
        proj = 0.0
        f = read['market_strength_factor']*read['sharp_money_factor']*read['psychological_factor']*read['line_value_factor']
        if pos=='QB':
            if 'player_pass_yds' in props: proj += props['player_pass_yds']['line']*f*self.dk['pass_yard']
            if 'player_pass_tds' in props: proj += props['player_pass_tds']['line']*f*self.dk['pass_td']
            proj += 1.0*self.dk['pass_int']
            if 'player_rush_yds' in props: proj += props['player_rush_yds']['line']*f*self.dk['rush_yard']
            if 'player_anytime_td' in props: proj += self._anytime_td_prob(props['player_anytime_td'])*self.dk['rush_td']
        elif pos in ('WR','TE'):
            if 'player_reception_yds' in props: proj += props['player_reception_yds']['line']*f*self.dk['rec_yard']
            if 'player_receptions' in props: proj += props['player_receptions']['line']*f*self.dk['reception']
            if 'player_anytime_td' in props: proj += self._anytime_td_prob(props['player_anytime_td'])*self.dk['rec_td']
        elif pos=='RB':
            if 'player_rush_yds' in props: proj += props['player_rush_yds']['line']*f*self.dk['rush_yard']
            if 'player_reception_yds' in props: proj += props['player_reception_yds']['line']*f*0.8*self.dk['rec_yard']
            # modest reception estimate for pass-catching RBs when receptions market absent
            if 'player_receptions' in props:
                proj += props['player_receptions']['line']*f*self.dk['reception']
            else:
                if player in {'Christian McCaffrey','Alvin Kamara'}: proj += 3.5*self.dk['reception']
                else: proj += 1.5*self.dk['reception']
            if 'player_anytime_td' in props: proj += self._anytime_td_prob(props['player_anytime_td'])*self.dk['rush_td']
        elif pos=='K':
            # Kicker approx from implied team total handled later via baseline
            pass
        return max(proj, 0.0)

    # ---------------------------- Roster -------------------------------

    def _dk_players(self) -> List[Dict[str,str]]:
        # Roster tailored to players with actual props in csvs/oddsnfl.txt for SF vs NO.
        # Do not include players without posted props.
        return [
            # 49ers (SF)
            {'name':'Mac Jones','position':'QB','team':'SF'},
            {'name':'Christian McCaffrey','position':'RB','team':'SF'},
            {'name':'Brian Robinson Jr.','position':'RB','team':'SF'},
            {'name':'Isaac Guerendo','position':'RB','team':'SF'},
            {'name':'Jordan James','position':'RB','team':'SF'},
            {'name':'Kyle Juszczyk','position':'RB','team':'SF'},
            {'name':'Ricky Pearsall','position':'WR','team':'SF'},
            {'name':'Jauan Jennings','position':'WR','team':'SF'},
            {'name':'Kendrick Bourne','position':'WR','team':'SF'},
            {'name':'Marquez Valdes-Scantling','position':'WR','team':'SF'},
            {'name':'Jordan Watkins','position':'WR','team':'SF'},
            {'name':'Skyy Moore','position':'WR','team':'SF'},
            {'name':'Jake Tonges','position':'TE','team':'SF'},
            {'name':'Luke Farrell','position':'TE','team':'SF'},
            # Saints (NO)
            {'name':'Spencer Rattler','position':'QB','team':'NO'},
            {'name':'Alvin Kamara','position':'RB','team':'NO'},
            {'name':'Kendre Miller','position':'RB','team':'NO'},
            {'name':'Devin Neal','position':'RB','team':'NO'},
            {'name':'Velus Jones Jr.','position':'RB','team':'NO'},
            {'name':'Chris Olave','position':'WR','team':'NO'},
            {'name':'Rashid Shaheed','position':'WR','team':'NO'},
            {'name':'Brandin Cooks','position':'WR','team':'NO'},
            {'name':'Devaughn Vele','position':'WR','team':'NO'},
            {'name':'Mason Tipton','position':'WR','team':'NO'},
            {'name':'Trey Palmer','position':'WR','team':'NO'},
            {'name':'Juwan Johnson','position':'TE','team':'NO'},
            {'name':'Moliki Matavao','position':'TE','team':'NO'},
            {'name':'Jack Stoll','position':'TE','team':'NO'},
        ]

    def _baseline_for(self, player: str, pos: str) -> float:
        if pos=='QB': return 18.0 if player in {'Brock Purdy','Derek Carr'} else 8.0
        if pos=='WR':
            if player in {'Brandon Aiyuk','Chris Olave','Deebo Samuel'}: return 12.0
            if player in {'Rashid Shaheed','Jauan Jennings'}: return 8.5
            return 5.0
        if pos=='RB':
            if player in {'Christian McCaffrey','Alvin Kamara'}: return 13.0
            if player in {'Elijah Mitchell','Jamaal Williams','Kendre Miller'}: return 7.0
            return 4.0
        if pos=='TE':
            if player in {'George Kittle','Juwan Johnson'}: return 8.5
            return 5.0
        if pos=='K': return 8.0
        return 5.0

    # -------------------------- Defense/DST ----------------------------

    def _defense_projection(self, defense_team: str, gc: GameContext, player_props: Dict[str, Dict[str, Any]]) -> float:
        # Opponent implied points
        opp_pts = gc.away_implied if defense_team == gc.home_team else gc.home_implied
        # Points allowed DK bucket
        if opp_pts <= 6: pts_allowed = self.dk['dst_pts_0']
        elif opp_pts <= 13: pts_allowed = self.dk['dst_pts_1_6']
        elif opp_pts <= 20: pts_allowed = self.dk['dst_pts_7_13']
        elif opp_pts <= 27: pts_allowed = self.dk['dst_pts_14_20']
        elif opp_pts <= 34: pts_allowed = self.dk['dst_pts_21_27']
        else: pts_allowed = self.dk['dst_pts_28_34']

        # Base sacks/turnovers scaled by edges + available sack props
        # Sum any player_sacks markets for that defense (rough proxy)
        sack_sum = 0.0
        for name, p in player_props.items():
            if 'player_sacks' in p:
                # attribute to team by last known roster pattern (simple heuristic)
                if (('49ers' in defense_team) or (defense_team in ('49ers','SF','San Francisco 49ers'))) and any(t in name for t in ['Bosa','Hargrave','Armstead','Greenlaw','Warner']):
                    sack_sum += float(p['player_sacks'].get('line') or 0)
                if (('Saints' in defense_team) or (defense_team in ('Saints','NO','New Orleans Saints'))) and any(t in name for t in ['Jordan','Granderson','Breezy','Demario']):
                    sack_sum += float(p['player_sacks'].get('line') or 0)
        # Edges
        is_niners = ('49ers' in defense_team) or (defense_team in ('49ers','SF','San Francisco 49ers'))
        if is_niners:
            base_sacks = 2.4 + 0.6*self.edges['niners_pass_edge']
        else:
            base_sacks = 2.2 + 0.5*self.edges['saints_protection_edge']
        sacks = max(base_sacks, sack_sum)
        # Turnovers modestly tied to sacks
        ints = 0.8 if is_niners else 0.7
        fumbles = 0.5
        tds = 0.18 if is_niners else 0.15

        return sacks*self.dk['dst_sack'] + ints*self.dk['dst_int'] + fumbles*self.dk['dst_fumble_rec'] + tds*self.dk['dst_td'] + pts_allowed

    # ----------------------------- Orchestration -----------------------

    def run(self) -> List[Tuple[str,float]]:
        # Prefer local text props file if present to avoid external API
        txt_path = 'csvs/oddsnfl.txt'
        if os.path.exists(txt_path):
            player_props, gc = self._parse_oddsnfl_file(txt_path)
        else:
            target, gc = self._find_game_and_context()
            props_raw = self._fetch_props_for_event(target['id'])
            player_props = self._process_sharp_props(props_raw)

        players = self._dk_players()
        # Ensure QBs have pass yard lines when books omit direct QB markets by deriving from teammates' receiving yards
        self._inject_qb_from_receivers(player_props, players)
        projections: Dict[str, float] = {}

        # Map team names to simple tags used in context multiplier
        home_tag = 'SF' if '49ers' in gc.home_team else 'NO'
        away_tag = 'SF' if '49ers' in gc.away_team else 'NO'

        for p in players:
            name, pos, team = p['name'], p['position'], p['team']
            props = player_props.get(self._normalize_name(name), {})

            # Only include players with actual props (no baselines)
            relevant_keys = {
                'player_pass_yds','player_pass_tds','player_pass_completions','player_pass_attempts',
                'player_pass_interceptions','player_rush_yds','player_rush_tds','player_rush_attempts',
                'player_receptions','player_reception_yds','player_reception_tds','player_anytime_td',
                'player_kicking_points','player_tackles_assists','player_sacks','player_defensive_interceptions',
                'player_longest_reception','player_longest_rush','player_longest_pass'
            }
            if (not props) or (len(set(props.keys()) & relevant_keys) == 0):
                continue
            # For kickers, require an actual kicking points market
            if pos == 'K' and 'player_kicking_points' not in props:
                continue

            read = self._elite_prop_read(name, props)
            base = self._calc_from_props(name, pos, props, read)
            ctx = self._apply_context_multiplier(name, pos, team, home_tag)
            proj = base * ctx
            if proj <= 0:
                continue
            projections[name] = round(proj, 2)

        # Add defenses
        projections['49ers'] = round(self._defense_projection(gc.home_team if '49ers' in gc.home_team else gc.away_team, gc, player_props), 2)
        projections['Saints'] = round(self._defense_projection(gc.home_team if 'Saints' in gc.home_team else gc.away_team, gc, player_props), 2)

        ranked = sorted(projections.items(), key=lambda x: x[1], reverse=True)
        return ranked


def main() -> None:
    system = EliteProjectionSystem()
    ranked = system.run()
    print("Player,Projection")
    for name, pts in ranked:
        print(f"{name},{pts:.2f}")
    # Save to CSV
    ts = datetime.now().strftime('%Y%m%d_%H%M')
    out = f"niners_saints_elite_projections_{ts}.csv"
    with open(out, 'w') as f:
        f.write("Player,Projection\n")
        for name, pts in ranked:
            f.write(f"{name},{pts:.2f}\n")


if __name__ == '__main__':
    main()

