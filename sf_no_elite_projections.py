#!/usr/bin/env python3
"""
ELITE NFL PROJECTION SYSTEM: San Francisco 49ers @ New Orleans Saints
Complete methodology with comprehensive prop analysis and team context
"""

import json
import pandas as pd
import numpy as np
from dataclasses import dataclass
from typing import Dict, List, Any, Optional, Tuple
import os
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

@dataclass
class GameContext:
    """Game context with market data and team ratings."""
    home_team: str = "Saints"
    away_team: str = "49ers"
    spread: float = -3.0  # 49ers favored by 3
    total: float = 40.5
    home_implied_total: float = 18.75  # (40.5 - 3) / 2
    away_implied_total: float = 21.75  # (40.5 + 3) / 2
    market_strength: float = 0.85
    market_uncertainty: float = 0.15

@dataclass
class TeamEdges:
    """Computed matchup edges for a team."""
    edge_pass: float = 0.0
    edge_rush: float = 0.0
    edge_rz: float = 0.0
    edge_explosive: float = 0.0
    edge_protection: float = 0.0
    edge_penalty: float = 0.0
    edge_plays: float = 0.0

class EliteProjectionSystem:
    """Elite NFL projection system with comprehensive prop analysis."""

    def __init__(self):
        self.game_context = GameContext()
        self.team_edges = self.get_team_edges()
        self.dk_scoring = self.get_dk_scoring()
        self.player_teams = self.get_player_teams()

    def get_dk_scoring(self) -> Dict[str, float]:
        """Complete DraftKings scoring system."""
        return {
            # Passing
            'pass_yard': 0.04, 'pass_td': 4, 'pass_int': -1,
            'pass_300_bonus': 3, 'pass_2pt': 2,

            # Rushing
            'rush_yard': 0.1, 'rush_td': 6, 'rush_100_bonus': 3,
            'rush_2pt': 2, 'fumble_lost': -1,

            # Receiving
            'rec_yard': 0.1, 'reception': 1, 'rec_td': 6,
            'rec_100_bonus': 3, 'rec_2pt': 2,

            # Special scores
            'return_td': 6, 'fumble_rec_td': 6,

            # Defense/Special Teams
            'dst_sack': 1, 'dst_int': 2, 'dst_fumble_rec': 2,
            'dst_td': 6, 'dst_safety': 2, 'dst_block': 2,
            'dst_pts_0': 10, 'dst_pts_1_6': 7, 'dst_pts_7_13': 4,
            'dst_pts_14_20': 1, 'dst_pts_21_27': 0, 'dst_pts_28_34': -1,
            'dst_pts_35_plus': -4
        }

    def get_player_teams(self) -> Dict[str, str]:
        """Map players to their teams."""
        return {
            # San Francisco 49ers
            'Christian McCaffrey': 'SF', 'Mac Jones': 'SF', 'Jauan Jennings': 'SF',
            'Ricky Pearsall': 'SF', 'Marquez Valdes-Scantling': 'SF', 'Kendrick Bourne': 'SF',
            'Brian Robinson': 'SF', 'Luke Farrell': 'SF', 'Isaac Guerendo': 'SF',
            'Adrian Martinez': 'SF', 'Jordan James': 'SF', 'Kyle Juszczyk': 'SF',
            'Jake Tonges': 'SF', 'Jordan Watkins': 'SF', 'Skyy Moore': 'SF',
            'Brayden Willis': 'SF', '49ers': 'SF', 'SF 49ers D/ST': 'SF',
            
            # New Orleans Saints
            'Alvin Kamara': 'NO', 'Spencer Rattler': 'NO', 'Rashid Shaheed': 'NO',
            'Chris Olave': 'NO', 'Juwan Johnson': 'NO', 'Brandin Cooks': 'NO',
            'Devaughn Vele': 'NO', 'Kendre Miller': 'NO', 'Tyler Shough': 'NO',
            'Devin Neal': 'NO', 'Velus Jones': 'NO', 'Mason Tipton': 'NO',
            'Trey Palmer': 'NO', 'Moliki Matavao': 'NO', 'Jack Stoll': 'NO',
            'Saints': 'NO', 'NO Saints D/ST': 'NO'
        }

    def get_team_edges(self) -> Dict[str, TeamEdges]:
        """Get team matchup edges based on advanced analytics."""
        return {
            'SF': TeamEdges(
                edge_pass=0.15,      # Strong passing offense vs weak Saints secondary
                edge_rush=0.25,      # McCaffrey vs Saints run defense
                edge_rz=0.20,        # Red zone efficiency edge
                edge_explosive=0.18, # Big play potential
                edge_protection=0.10, # O-line vs Saints pass rush
                edge_penalty=-0.05,  # Slight penalty disadvantage
                edge_plays=0.12      # More plays expected
            ),
            'NO': TeamEdges(
                edge_pass=-0.10,     # Weaker passing vs 49ers secondary
                edge_rush=0.05,      # Kamara can still produce
                edge_rz=-0.15,       # Red zone struggles
                edge_explosive=-0.12, # Limited big play ability
                edge_protection=-0.20, # O-line vs 49ers pass rush
                edge_penalty=0.08,   # More disciplined
                edge_plays=-0.08     # Fewer plays expected
            )
        }

    def odds_to_probability(self, odds: int) -> float:
        """Convert American odds to implied probability."""
        if odds > 0:
            return 100 / (odds + 100)
        else:
            return abs(odds) / (abs(odds) + 100)

    def calculate_anytime_td_probability(self, odds: int) -> float:
        """Calculate anytime TD probability with market adjustments."""
        if odds is None:
            return 0.0
        
        implied_prob = self.odds_to_probability(odds)
        
        # Apply sharp money adjustments based on odds ranges
        if odds <= -150:  # Heavy favorites
            adjusted_prob = implied_prob * 0.95  # Slight reduction for juice
        elif odds <= 0:   # Favorites
            adjusted_prob = implied_prob * 0.92
        elif odds <= 300: # Moderate underdogs
            adjusted_prob = implied_prob * 0.88
        else:             # Long shots
            adjusted_prob = implied_prob * 0.85
            
        return min(adjusted_prob, 0.85)  # Cap at 85%

    def extract_prop_signals(self, player_name: str, odds_data: Dict) -> Dict[str, Any]:
        """Extract comprehensive prop signals for a player."""
        signals = {
            'passing_yards': 0, 'passing_tds': 0, 'rushing_yards': 0,
            'receiving_yards': 0, 'receptions': 0, 'anytime_td_prob': 0,
            'market_strength': 0, 'sharp_money_factor': 1.0
        }
        
        # Extract passing yards
        for passer in odds_data.get('player_props', {}).get('passing_yards', []):
            if passer['player_name'] == player_name:
                for line in passer['lines']:
                    if line['odds'] is not None:
                        signals['passing_yards'] = line['yards']
                        signals['market_strength'] += 0.3
                        break
        
        # Extract passing TDs
        for passer in odds_data.get('player_props', {}).get('passing_touchdowns', []):
            if passer['player_name'] == player_name:
                for line in passer['lines']:
                    if line['odds'] is not None:
                        signals['passing_tds'] = line['touchdowns']
                        signals['market_strength'] += 0.2
                        break
        
        # Extract rushing yards
        for rusher in odds_data.get('player_props', {}).get('rushing_yards', []):
            if rusher['player_name'] == player_name:
                for line in rusher['lines']:
                    if line['odds'] is not None:
                        signals['rushing_yards'] = line['yards']
                        signals['market_strength'] += 0.25
                        break
        
        # Extract receiving yards
        for receiver in odds_data.get('player_props', {}).get('receiving_yards', []):
            if receiver['player_name'] == player_name:
                for line in receiver['lines']:
                    if line['odds'] is not None:
                        signals['receiving_yards'] = line['yards']
                        signals['market_strength'] += 0.25
                        break
        
        # Extract receptions
        for receiver in odds_data.get('player_props', {}).get('receptions', []):
            if receiver['player_name'] == player_name:
                for line in receiver['lines']:
                    if line['odds'] is not None:
                        signals['receptions'] = line['receptions']
                        signals['market_strength'] += 0.2
                        break
        
        # Extract anytime TD probability
        for td_player in odds_data.get('player_touchdown_scorer_odds', []):
            if td_player['player_name'] == player_name:
                if td_player.get('anytime_td_scorer'):
                    signals['anytime_td_prob'] = self.calculate_anytime_td_probability(
                        td_player['anytime_td_scorer']
                    )
                    signals['market_strength'] += 0.3
                break
        
        # Calculate sharp money factor based on line quality and market depth
        if signals['market_strength'] > 1.0:
            signals['sharp_money_factor'] = 1.1  # Strong market = sharp money
        elif signals['market_strength'] > 0.5:
            signals['sharp_money_factor'] = 1.05  # Moderate market
        else:
            signals['sharp_money_factor'] = 0.95  # Weak market = public money
        
        return signals

    def calculate_prop_based_projection(self, player_name: str, signals: Dict[str, Any]) -> float:
        """Calculate DraftKings fantasy projection from prop signals."""
        projection = 0.0
        team = self.player_teams.get(player_name, 'UNK')

        # Apply team context multipliers
        team_edges = self.team_edges.get(team, TeamEdges())

        # CRITICAL: Low total game adjustment (40.5 total)
        low_total_factor = 0.8  # Reduce all projections by 20% for low-scoring environment

        # Passing yards and TDs
        if signals['passing_yards'] > 0:
            pass_yards_proj = signals['passing_yards'] * (1 + team_edges.edge_pass)
            projection += pass_yards_proj * self.dk_scoring['pass_yard']

            # 300+ yard bonus
            if pass_yards_proj >= 300:
                projection += self.dk_scoring['pass_300_bonus']

            # Passing TDs - use market line or estimate from yards
            if signals['passing_tds'] > 0:
                pass_tds = signals['passing_tds'] * (1 + team_edges.edge_rz)
            else:
                # Estimate TDs from yards (roughly 1 TD per 250 yards)
                pass_tds = (pass_yards_proj / 250) * (1 + team_edges.edge_rz)

            projection += pass_tds * self.dk_scoring['pass_td']

        # Rushing yards and TDs
        if signals['rushing_yards'] > 0:
            rush_yards_proj = signals['rushing_yards'] * (1 + team_edges.edge_rush)
            projection += rush_yards_proj * self.dk_scoring['rush_yard']

            # 100+ yard bonus
            if rush_yards_proj >= 100:
                projection += self.dk_scoring['rush_100_bonus']

            # Estimate rushing TDs (roughly 1 TD per 80 yards for RBs, 120 for others)
            td_rate = 80 if 'RB' in player_name or player_name in ['Christian McCaffrey', 'Alvin Kamara'] else 120
            rush_tds = (rush_yards_proj / td_rate) * (1 + team_edges.edge_rz)
            projection += rush_tds * self.dk_scoring['rush_td']

        # Receiving yards, receptions, and TDs
        if signals['receiving_yards'] > 0 or signals['receptions'] > 0:
            rec_yards_proj = signals['receiving_yards'] * (1 + team_edges.edge_pass)
            receptions_proj = signals['receptions'] * (1 + team_edges.edge_plays)

            projection += rec_yards_proj * self.dk_scoring['rec_yard']
            projection += receptions_proj * self.dk_scoring['reception']

            # 100+ yard bonus
            if rec_yards_proj >= 100:
                projection += self.dk_scoring['rec_100_bonus']

            # Estimate receiving TDs (roughly 1 TD per 100 yards)
            rec_tds = (rec_yards_proj / 100) * (1 + team_edges.edge_rz)
            projection += rec_tds * self.dk_scoring['rec_td']

        # Add anytime TD probability bonus
        if signals['anytime_td_prob'] > 0:
            td_bonus = signals['anytime_td_prob'] * 6  # 6 points for TD
            projection += td_bonus

        # Apply sharp money factor and low total adjustment
        projection *= signals['sharp_money_factor'] * low_total_factor

        return max(projection, 0.0)

    def create_defense_projection(self, team: str, opponent_total: float) -> float:
        """Create team defense projection based on opponent total and context."""
        base_projection = 8.0  # Base D/ST points

        # Points allowed scoring (most important factor)
        if opponent_total <= 6:
            points_bonus = self.dk_scoring['dst_pts_0']  # 10 pts for 0 points allowed
        elif opponent_total <= 13:
            points_bonus = self.dk_scoring['dst_pts_1_6']  # 7 pts for 1-6 points
        elif opponent_total <= 20:
            points_bonus = self.dk_scoring['dst_pts_7_13']  # 4 pts for 7-13 points
        elif opponent_total <= 27:
            points_bonus = self.dk_scoring['dst_pts_14_20']  # 1 pt for 14-20 points
        elif opponent_total <= 34:
            points_bonus = self.dk_scoring['dst_pts_21_27']  # 0 pts for 21-27 points
        else:
            points_bonus = self.dk_scoring['dst_pts_35_plus']  # -4 pts for 35+ points

        # Add sacks (estimate 2.5 sacks per game average)
        sacks_projection = 2.5
        if team == 'SF':
            sacks_projection = 3.2  # 49ers have strong pass rush
        elif team == 'NO':
            sacks_projection = 2.0  # Saints weaker pass rush

        # Add interceptions (estimate 0.8 per game average)
        int_projection = 0.8
        if team == 'SF':
            int_projection = 1.1  # 49ers secondary advantage
        elif team == 'NO':
            int_projection = 0.6  # Saints secondary disadvantage

        # Add fumble recoveries (estimate 0.5 per game)
        fumble_projection = 0.5

        # Calculate total projection
        total_projection = (
            base_projection +
            points_bonus +
            (sacks_projection * self.dk_scoring['dst_sack']) +
            (int_projection * self.dk_scoring['dst_int']) +
            (fumble_projection * self.dk_scoring['dst_fumble_rec'])
        )

        return max(total_projection, 2.0)  # Minimum 2 points

    def get_baseline_projection(self, player_name: str) -> float:
        """Get baseline projection for players without props."""
        # Position-based baselines
        baselines = {
            # QBs
            'Tyler Shough': 8.5, 'Adrian Martinez': 6.2,

            # RBs
            'Brian Robinson': 9.8, 'Isaac Guerendo': 7.5, 'Jordan James': 5.2,
            'Kendre Miller': 8.1, 'Devin Neal': 4.8, 'Velus Jones': 3.5,

            # WRs
            'Marquez Valdes-Scantling': 8.9, 'Kendrick Bourne': 7.6,
            'Devaughn Vele': 6.2, 'Mason Tipton': 4.8, 'Trey Palmer': 5.1,
            'Jordan Watkins': 3.9, 'Skyy Moore': 4.2,

            # TEs
            'Luke Farrell': 6.8, 'Jake Tonges': 5.9, 'Brayden Willis': 3.2,
            'Moliki Matavao': 2.8, 'Jack Stoll': 3.5,

            # FB
            'Kyle Juszczyk': 4.1
        }

        return baselines.get(player_name, 2.0)

    def apply_team_context_multipliers(self, player_name: str, team: str) -> float:
        """Apply team-specific context multipliers with game total adjustment."""
        team_edges = self.team_edges.get(team, TeamEdges())

        # CRITICAL: Low total adjustment (40.5 is very low)
        # Average NFL total is ~45, so this is significantly under
        low_total_penalty = 0.75  # 25% reduction for low-scoring environment

        # Base multiplier from team strength (reduced for low total)
        if team == 'SF':
            base_multiplier = 0.85 * low_total_penalty  # 49ers favored but low total
        else:
            base_multiplier = 0.75 * low_total_penalty  # Saints underdog in low total

        # Position-specific adjustments (more conservative)
        if any(pos in player_name.lower() for pos in ['qb', 'quarterback']):
            # QBs suffer most in low-scoring games
            multiplier = base_multiplier * (1 + team_edges.edge_pass * 0.5)
        elif any(pos in player_name.lower() for pos in ['rb', 'running']):
            # RBs less affected by low totals (more volume)
            multiplier = base_multiplier * (1 + team_edges.edge_rush * 0.8)
        elif any(pos in player_name.lower() for pos in ['wr', 'te', 'receiver']):
            # Pass catchers hurt by low totals
            multiplier = base_multiplier * (1 + team_edges.edge_pass * 0.4)
        else:
            multiplier = base_multiplier

        return max(multiplier, 0.4)  # Lower floor for low-scoring game

    def create_projections(self) -> List[Tuple[str, float]]:
        """Create elite DraftKings projections with comprehensive methodology."""

        print("🏈 ELITE NFL PROJECTION SYSTEM: SF @ NO")
        print("=" * 60)

        # Load odds data
        try:
            with open('csvs/oddsnfl.txt', 'r', encoding='utf-8-sig') as f:
                odds_data = json.load(f)
        except FileNotFoundError:
            print("❌ Odds file not found!")
            return []
        except json.JSONDecodeError as e:
            print(f"❌ JSON decode error: {e}")
            return []

        print(f"📊 Game Context:")
        print(f"   Spread: SF {self.game_context.spread}")
        print(f"   Total: {self.game_context.total}")
        print(f"   SF Implied: {self.game_context.away_implied_total}")
        print(f"   NO Implied: {self.game_context.home_implied_total}")
        print()

        projections = []
        detailed_analysis = {}

        # Process all players with props
        all_players = set()

        # Collect all players from props
        for prop_type in ['passing_yards', 'passing_touchdowns', 'rushing_yards',
                         'receiving_yards', 'receptions', 'rushing_and_receiving_yards']:
            for player_data in odds_data.get('player_props', {}).get(prop_type, []):
                all_players.add(player_data['player_name'])

        # Add players from TD odds
        for td_data in odds_data.get('player_touchdown_scorer_odds', []):
            if td_data['player_name'] not in ['No Touchdown Scorer']:
                all_players.add(td_data['player_name'])

        print(f"🎯 Processing {len(all_players)} players with market data...")
        print()

        # Process each player
        for player_name in sorted(all_players):
            if player_name in ['No Touchdown Scorer']:
                continue

            team = self.player_teams.get(player_name, 'UNK')
            if team == 'UNK':
                continue

            print(f"📈 {player_name} ({team})")

            # Extract prop signals
            signals = self.extract_prop_signals(player_name, odds_data)

            # Calculate projection
            if any(signals[key] > 0 for key in ['passing_yards', 'rushing_yards', 'receiving_yards', 'receptions']):
                # Player has significant props - use prop-based projection
                base_projection = self.calculate_prop_based_projection(player_name, signals)
                source = 'props'
            else:
                # Player has limited props - use baseline + TD probability
                base_projection = self.get_baseline_projection(player_name)
                if signals['anytime_td_prob'] > 0:
                    base_projection += signals['anytime_td_prob'] * 6
                source = 'baseline+td'

            # Apply team context
            context_multiplier = self.apply_team_context_multipliers(player_name, team)
            final_projection = base_projection * context_multiplier

            # Store projection
            projections.append((player_name, final_projection))

            # Store detailed analysis
            detailed_analysis[player_name] = {
                'base_projection': base_projection,
                'context_multiplier': context_multiplier,
                'signals': signals,
                'team': team,
                'source': source
            }

            print(f"   Base: {base_projection:.2f} × {context_multiplier:.3f} = {final_projection:.2f} pts")
            if signals['anytime_td_prob'] > 0:
                print(f"   TD Prob: {signals['anytime_td_prob']:.1%}")
            print()

        # Add team defenses
        print("🛡️  TEAM DEFENSES")
        print("-" * 30)

        # 49ers Defense
        sf_def_proj = self.create_defense_projection('SF', self.game_context.home_implied_total)
        projections.append(('49ers', sf_def_proj))
        print(f"📈 49ers D/ST: {sf_def_proj:.2f} pts")
        print(f"   vs NO implied {self.game_context.home_implied_total} pts")

        # Saints Defense
        no_def_proj = self.create_defense_projection('NO', self.game_context.away_implied_total)
        projections.append(('Saints', no_def_proj))
        print(f"📈 Saints D/ST: {no_def_proj:.2f} pts")
        print(f"   vs SF implied {self.game_context.away_implied_total} pts")
        print()

        # Sort by projection descending
        projections.sort(key=lambda x: x[1], reverse=True)

        print("🏆 FINAL ELITE PROJECTIONS")
        print("=" * 60)
        for player, proj in projections:
            print(f"{player},{proj:.2f}")

        return projections


def main():
    """Run the elite projection system."""
    system = EliteProjectionSystem()
    projections = system.create_projections()

    if projections:
        print("\n" + "="*60)
        print("COMMA-SEPARATED OUTPUT:")
        print("="*60)
        for player, proj in projections:
            print(f"{player},{proj:.2f}")


if __name__ == "__main__":
    main()
