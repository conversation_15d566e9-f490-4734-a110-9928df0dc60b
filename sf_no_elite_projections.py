#!/usr/bin/env python3
"""
SIMPLE PROP-DRIVEN PROJECTIONS: SF @ NO
Just use the props + team rankings. No guessing.
"""

import json
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

class SimpleProjectionSystem:
    """Simple prop-driven projections with team rankings."""

    def __init__(self):
        self.dk_scoring = self.get_dk_scoring()
        self.player_teams = self.get_player_teams()
        self.team_rankings = self.load_team_rankings()

    def get_dk_scoring(self) -> Dict[str, float]:
        """Complete DraftKings scoring system."""
        return {
            # Passing
            'pass_yard': 0.04, 'pass_td': 4, 'pass_int': -1,
            'pass_300_bonus': 3, 'pass_2pt': 2,

            # Rushing
            'rush_yard': 0.1, 'rush_td': 6, 'rush_100_bonus': 3,
            'rush_2pt': 2, 'fumble_lost': -1,

            # Receiving
            'rec_yard': 0.1, 'reception': 1, 'rec_td': 6,
            'rec_100_bonus': 3, 'rec_2pt': 2,

            # Special scores
            'return_td': 6, 'fumble_rec_td': 6,

            # Defense/Special Teams
            'dst_sack': 1, 'dst_int': 2, 'dst_fumble_rec': 2,
            'dst_td': 6, 'dst_safety': 2, 'dst_block': 2,
            'dst_pts_0': 10, 'dst_pts_1_6': 7, 'dst_pts_7_13': 4,
            'dst_pts_14_20': 1, 'dst_pts_21_27': 0, 'dst_pts_28_34': -1,
            'dst_pts_35_plus': -4
        }

    def load_team_rankings(self) -> Dict[str, Dict]:
        """Load team rankings from parquet file."""
        try:
            df = pd.read_parquet('models/team_ranks.parquet')
            rankings = {}
            for _, row in df.iterrows():
                team = row['team']
                if team in ['49ers', 'Saints']:
                    rankings[team] = {
                        'off_pass_rank': row['OFF_nyd_rating_rank'],
                        'off_rush_rank': row['OFF_rush_ypa_rating_rank'],
                        'def_pass_rank': row['DEF_nyd_allowed_rating_rank'],
                        'def_rush_rank': row['DEF_rush_ypa_allowed_rating_rank'],
                        'off_rz_rank': row['OFF_rz_td_pct_rating_rank'],
                        'def_rz_rank': row['DEF_rz_td_pct_allowed_rating_rank']
                    }
            return rankings
        except:
            return {}

    def get_player_teams(self) -> Dict[str, str]:
        """Map players to their teams."""
        return {
            # San Francisco 49ers
            'Christian McCaffrey': '49ers', 'Mac Jones': '49ers', 'Jauan Jennings': '49ers',
            'Ricky Pearsall': '49ers', 'Marquez Valdes-Scantling': '49ers', 'Kendrick Bourne': '49ers',
            'Brian Robinson': '49ers', 'Luke Farrell': '49ers', 'Isaac Guerendo': '49ers',
            'Adrian Martinez': '49ers', 'Jordan James': '49ers', 'Kyle Juszczyk': '49ers',
            'Jake Tonges': '49ers', 'Jordan Watkins': '49ers', 'Skyy Moore': '49ers',
            'Brayden Willis': '49ers', '49ers': '49ers', 'SF 49ers D/ST': '49ers',

            # New Orleans Saints
            'Alvin Kamara': 'Saints', 'Spencer Rattler': 'Saints', 'Rashid Shaheed': 'Saints',
            'Chris Olave': 'Saints', 'Juwan Johnson': 'Saints', 'Brandin Cooks': 'Saints',
            'Devaughn Vele': 'Saints', 'Kendre Miller': 'Saints', 'Tyler Shough': 'Saints',
            'Devin Neal': 'Saints', 'Velus Jones': 'Saints', 'Mason Tipton': 'Saints',
            'Trey Palmer': 'Saints', 'Moliki Matavao': 'Saints', 'Jack Stoll': 'Saints',
            'Saints': 'Saints', 'NO Saints D/ST': 'Saints'
        }

    def get_team_multiplier(self, player_team: str, stat_type: str) -> float:
        """Get team ranking multiplier for a stat type."""
        if player_team not in self.team_rankings:
            return 1.0

        rankings = self.team_rankings[player_team]

        # Convert ranks to multipliers (lower rank = better = higher multiplier)
        if stat_type == 'passing':
            rank = rankings['off_pass_rank']
        elif stat_type == 'rushing':
            rank = rankings['off_rush_rank']
        elif stat_type == 'receiving':
            rank = rankings['off_pass_rank']  # Receiving tied to passing offense
        elif stat_type == 'red_zone':
            rank = rankings['off_rz_rank']
        else:
            return 1.0

        # Convert rank (1-32) to multiplier (1.15 to 0.85)
        # Rank 1 = 1.15x, Rank 16 = 1.0x, Rank 32 = 0.85x
        multiplier = 1.15 - (rank - 1) * (0.30 / 31)
        return max(0.85, min(1.15, multiplier))

    def convert_props_to_fantasy_points(self, player_name: str, odds_data: Dict) -> float:
        """Convert player props directly to fantasy points."""
        points = 0.0
        team = self.player_teams.get(player_name, '')

        # Passing yards
        for passer in odds_data.get('player_props', {}).get('passing_yards', []):
            if passer['player_name'] == player_name:
                for line in passer['lines']:
                    if line['odds'] is not None:
                        yards = line['yards']
                        multiplier = self.get_team_multiplier(team, 'passing')
                        points += (yards * multiplier) * self.dk_scoring['pass_yard']
                        if yards >= 300:
                            points += self.dk_scoring['pass_300_bonus']
                        break

        # Passing TDs
        for passer in odds_data.get('player_props', {}).get('passing_touchdowns', []):
            if passer['player_name'] == player_name:
                for line in passer['lines']:
                    if line['odds'] is not None:
                        tds = line['touchdowns']
                        multiplier = self.get_team_multiplier(team, 'red_zone')
                        points += (tds * multiplier) * self.dk_scoring['pass_td']
                        break

        # Rushing yards
        for rusher in odds_data.get('player_props', {}).get('rushing_yards', []):
            if rusher['player_name'] == player_name:
                for line in rusher['lines']:
                    if line['odds'] is not None:
                        yards = line['yards']
                        multiplier = self.get_team_multiplier(team, 'rushing')
                        points += (yards * multiplier) * self.dk_scoring['rush_yard']
                        if yards >= 100:
                            points += self.dk_scoring['rush_100_bonus']
                        # Estimate TDs from yards
                        td_prob = (yards / 80) * multiplier * self.get_team_multiplier(team, 'red_zone')
                        points += td_prob * self.dk_scoring['rush_td']
                        break

        # Receiving yards
        for receiver in odds_data.get('player_props', {}).get('receiving_yards', []):
            if receiver['player_name'] == player_name:
                for line in receiver['lines']:
                    if line['odds'] is not None:
                        yards = line['yards']
                        multiplier = self.get_team_multiplier(team, 'receiving')
                        points += (yards * multiplier) * self.dk_scoring['rec_yard']
                        if yards >= 100:
                            points += self.dk_scoring['rec_100_bonus']
                        # Estimate TDs from yards
                        td_prob = (yards / 100) * multiplier * self.get_team_multiplier(team, 'red_zone')
                        points += td_prob * self.dk_scoring['rec_td']
                        break

        # Receptions
        for receiver in odds_data.get('player_props', {}).get('receptions', []):
            if receiver['player_name'] == player_name:
                for line in receiver['lines']:
                    if line['odds'] is not None:
                        recs = line['receptions']
                        multiplier = self.get_team_multiplier(team, 'receiving')
                        points += (recs * multiplier) * self.dk_scoring['reception']
                        break

        # Anytime TD
        for td_player in odds_data.get('player_touchdown_scorer_odds', []):
            if td_player['player_name'] == player_name and td_player.get('anytime_td_scorer'):
                odds = td_player['anytime_td_scorer']
                if odds <= 0:
                    prob = abs(odds) / (abs(odds) + 100)
                else:
                    prob = 100 / (odds + 100)
                multiplier = self.get_team_multiplier(team, 'red_zone')
                points += (prob * multiplier) * 6  # 6 points for TD
                break

        return points

    def create_defense_projection(self, team: str) -> float:
        """Simple defense projection based on team rankings."""
        if team not in self.team_rankings:
            return 8.0

        rankings = self.team_rankings[team]

        # Better defense ranks = higher projections
        avg_def_rank = (rankings['def_pass_rank'] + rankings['def_rush_rank']) / 2

        # Convert rank to projection (rank 1 = 12 pts, rank 32 = 4 pts)
        base_projection = 12 - (avg_def_rank - 1) * (8 / 31)

        return max(4.0, min(12.0, base_projection))

    def create_projections(self) -> List[Tuple[str, float]]:
        """Create simple prop-driven projections."""

        print("🏈 SIMPLE PROP-DRIVEN PROJECTIONS: SF @ NO")
        print("=" * 50)

        # Load odds data
        try:
            with open('csvs/oddsnfl.txt', 'r', encoding='utf-8-sig') as f:
                odds_data = json.load(f)
        except:
            print("❌ Could not load odds data")
            return []

        projections = []

        # Get all players with props
        all_players = set()
        for prop_type in ['passing_yards', 'passing_touchdowns', 'rushing_yards',
                         'receiving_yards', 'receptions']:
            for player_data in odds_data.get('player_props', {}).get(prop_type, []):
                all_players.add(player_data['player_name'])

        for td_data in odds_data.get('player_touchdown_scorer_odds', []):
            if td_data['player_name'] not in ['No Touchdown Scorer']:
                all_players.add(td_data['player_name'])

        print(f"📊 Processing {len(all_players)} players...")
        print()

        # Process each player
        for player_name in sorted(all_players):
            if player_name in ['No Touchdown Scorer']:
                continue

            team = self.player_teams.get(player_name, '')
            if not team:
                continue

            # Convert props to fantasy points
            fantasy_points = self.convert_props_to_fantasy_points(player_name, odds_data)

            if fantasy_points > 0:
                projections.append((player_name, fantasy_points))
                print(f"📈 {player_name} ({team}): {fantasy_points:.2f} pts")

        # Add defenses
        sf_def = self.create_defense_projection('49ers')
        no_def = self.create_defense_projection('Saints')

        projections.append(('49ers', sf_def))
        projections.append(('Saints', no_def))

        print()
        print(f"🛡️  49ers D/ST: {sf_def:.2f} pts")
        print(f"🛡️  Saints D/ST: {no_def:.2f} pts")

        # Sort by projection
        projections.sort(key=lambda x: x[1], reverse=True)

        return projections

def main():
    """Run the simple projection system."""
    system = SimpleProjectionSystem()
    projections = system.create_projections()

    if projections:
        print("\n" + "="*50)
        print("FINAL PROJECTIONS:")
        print("="*50)
        for player, proj in projections:
            print(f"{player},{proj:.2f}")


if __name__ == "__main__":
    main()


